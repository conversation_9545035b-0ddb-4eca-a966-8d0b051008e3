import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../services/auth_service.dart';
import '../../services/firebase_service.dart';
import '../../services/ads_service.dart';
import '../../models/user_model.dart';
import '../plan/ai_plan_screen.dart';
import '../progress/progress_screen.dart';
import '../recipes/recipes_screen.dart';

/// الشاشة الرئيسية للتطبيق
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  UserModel? _userProfile;
  bool _isLoading = true;

  final List<Widget> _screens = [
    const HomeTab(),
    const AIPlanScreen(),
    const ProgressScreen(),
    const RecipesScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserProfile() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();

      if (authService.currentUserId != null) {
        final profile = await firebaseService.getUserProfile(authService.currentUserId!);
        if (mounted) {
          setState(() {
            _userProfile = profile;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        AppHelpers.showErrorMessage(context, 'فشل في تحميل البيانات: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.fitness_center),
            label: 'خطتي',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.trending_up),
            label: 'التقدم',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.restaurant_menu),
            label: 'الوصفات',
          ),
        ],
      ),
    );
  }
}

/// تبويب الشاشة الرئيسية
class HomeTab extends StatefulWidget {
  const HomeTab({super.key});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  UserModel? _userProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserProfile() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final firebaseService = FirebaseService();

      if (authService.currentUserId != null) {
        final profile = await firebaseService.getUserProfile(authService.currentUserId!);
        if (mounted) {
          setState(() {
            _userProfile = profile;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        AppHelpers.showErrorMessage(context, 'فشل في تحميل البيانات: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${AppHelpers.getGreeting()}، ${_userProfile?.name ?? 'مستخدم'}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: فتح شاشة الإشعارات
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {
              // TODO: فتح شاشة الإعدادات
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadUserProfile,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة الملخص اليومي
              _buildDailySummaryCard(),

              const SizedBox(height: AppConstants.paddingMedium),

              // بطاقة الإحصائيات السريعة
              _buildQuickStatsCard(),

              const SizedBox(height: AppConstants.paddingMedium),

              // الإجراءات السريعة
              _buildQuickActionsSection(),

              const SizedBox(height: AppConstants.paddingMedium),

              // التقدم الأسبوعي
              _buildWeeklyProgressSection(),

              const SizedBox(height: AppConstants.paddingMedium),

              // إعلان البانر
              const BannerAdWidget(
                margin: EdgeInsets.symmetric(vertical: AppConstants.paddingSmall),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بطاقة الملخص اليومي
  Widget _buildDailySummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  AppHelpers.getTimeIcon(),
                  color: AppConstants.primaryColor,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Text(
                  'ملخص اليوم',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'السعرات المستهلكة',
                    '1,250',
                    '/${_userProfile?.dailyCalories ?? 2000}',
                    Icons.local_fire_department,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'التمارين',
                    '30',
                    ' دقيقة',
                    Icons.fitness_center,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر في الملخص
  Widget _buildSummaryItem(String title, String value, String suffix, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text.rich(
          TextSpan(
            text: value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            children: [
              TextSpan(
                text: suffix,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بطاقة الإحصائيات السريعة
  Widget _buildQuickStatsCard() {
    if (_userProfile == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائياتك',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'مؤشر كتلة الجسم',
                    _userProfile!.bmi.toStringAsFixed(1),
                    _userProfile!.bmiCategory,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الوزن الحالي',
                    '${_userProfile!.currentWeight.toStringAsFixed(1)} كجم',
                    'الهدف: ${_userProfile!.targetWeight.toStringAsFixed(1)} كجم',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر إحصائية
  Widget _buildStatItem(String title, String value, String subtitle) {
    return Column(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppConstants.textSecondaryColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// قسم الإجراءات السريعة
  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'إنشاء خطة جديدة',
                Icons.auto_awesome,
                AppConstants.primaryColor,
                () {
                  // TODO: الانتقال لشاشة إنشاء الخطة
                },
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            Expanded(
              child: _buildQuickActionCard(
                'تسجيل الوزن',
                Icons.monitor_weight,
                Colors.blue,
                () {
                  // TODO: فتح حوار تسجيل الوزن
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة إجراء سريع
  Widget _buildQuickActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// قسم التقدم الأسبوعي
  Widget _buildWeeklyProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقدم الأسبوعي',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: AppConstants.paddingMedium),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              children: [
                Text(
                  'سيتم عرض الرسم البياني للتقدم هنا',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                ElevatedButton(
                  onPressed: () {
                    // TODO: الانتقال لشاشة التقدم التفصيلية
                  },
                  child: const Text('عرض التفاصيل'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
