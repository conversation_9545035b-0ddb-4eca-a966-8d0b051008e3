import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

/// خدمة الإشعارات المحلية والسحابية
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة المناطق الزمنية
      tz.initializeTimeZones();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // تهيئة Firebase Messaging
      await _initializeFirebaseMessaging();

      _isInitialized = true;
      debugPrint('تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      debugPrint('فشل في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // طلب الأذونات
    await _requestPermissions();
  }

  /// تهيئة Firebase Messaging
  Future<void> _initializeFirebaseMessaging() async {
    // طلب الأذونات
    await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    // الحصول على FCM Token
    final token = await _firebaseMessaging.getToken();
    debugPrint('FCM Token: $token');

    // الاستماع للرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // الاستماع للرسائل عند فتح التطبيق
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // التحقق من الرسائل عند بدء التطبيق
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }

  /// معالجة الإشعارات في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('تم استلام رسالة في المقدمة: ${message.messageId}');
    
    // عرض إشعار محلي
    showNotification(
      title: message.notification?.title ?? 'صحتك AI',
      body: message.notification?.body ?? 'لديك إشعار جديد',
      payload: message.data.toString(),
    );
  }

  /// معالجة النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');
    // TODO: التنقل للشاشة المناسبة حسب نوع الإشعار
  }

  /// معالجة فتح التطبيق من الإشعار
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('تم فتح التطبيق من الإشعار: ${message.messageId}');
    // TODO: التنقل للشاشة المناسبة حسب البيانات
  }

  /// عرض إشعار فوري
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    int id = 0,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'sahtak_ai_channel',
      'صحتك AI',
      channelDescription: 'إشعارات تطبيق صحتك AI',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(id, title, body, details, payload: payload);
  }

  /// جدولة إشعار لوقت محدد
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'sahtak_ai_scheduled',
      'إشعارات مجدولة',
      channelDescription: 'إشعارات مجدولة لتطبيق صحتك AI',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// جدولة إشعار يومي
  Future<void> scheduleDailyNotification({
    required int id,
    required String title,
    required String body,
    required int hour,
    required int minute,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'sahtak_ai_daily',
      'إشعارات يومية',
      channelDescription: 'إشعارات يومية لتطبيق صحتك AI',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    final now = tz.TZDateTime.now(tz.local);
    var scheduledDate = tz.TZDateTime(tz.local, now.year, now.month, now.day, hour, minute);
    
    // إذا كان الوقت قد مضى اليوم، جدول للغد
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    await _localNotifications.zonedSchedule(
      id,
      title,
      body,
      scheduledDate,
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  /// إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// الحصول على الإشعارات المجدولة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _localNotifications.pendingNotificationRequests();
  }

  /// إشعارات التمارين
  Future<void> scheduleWorkoutReminders({
    required List<int> workoutDays, // أيام الأسبوع (1-7)
    required int hour,
    required int minute,
  }) async {
    // إلغاء الإشعارات السابقة
    for (int i = 100; i < 107; i++) {
      await cancelNotification(i);
    }

    // جدولة إشعارات جديدة
    for (final day in workoutDays) {
      await scheduleDailyNotification(
        id: 100 + day,
        title: '🏋️ وقت التمرين!',
        body: 'حان وقت تمرين اليوم. ابدأ الآن لتحقيق أهدافك!',
        hour: hour,
        minute: minute,
        payload: 'workout_reminder',
      );
    }
  }

  /// إشعارات الوجبات
  Future<void> scheduleMealReminders({
    required List<Map<String, dynamic>> mealTimes, // [{name: 'الإفطار', hour: 8, minute: 0}]
  }) async {
    // إلغاء الإشعارات السابقة
    for (int i = 200; i < 210; i++) {
      await cancelNotification(i);
    }

    // جدولة إشعارات جديدة
    for (int i = 0; i < mealTimes.length; i++) {
      final meal = mealTimes[i];
      await scheduleDailyNotification(
        id: 200 + i,
        title: '🍽️ وقت ${meal['name']}!',
        body: 'حان وقت تناول ${meal['name']}. تذكر اتباع خطتك الغذائية!',
        hour: meal['hour'],
        minute: meal['minute'],
        payload: 'meal_reminder_${meal['name']}',
      );
    }
  }

  /// إشعار تسجيل الوزن
  Future<void> scheduleWeightTrackingReminder({
    required int hour,
    required int minute,
    int frequency = 7, // كل كم يوم
  }) async {
    await cancelNotification(300);
    
    await scheduleDailyNotification(
      id: 300,
      title: '⚖️ تذكير تسجيل الوزن',
      body: 'سجل وزنك اليوم لتتبع تقدمك نحو هدفك!',
      hour: hour,
      minute: minute,
      payload: 'weight_tracking',
    );
  }

  /// إشعار شرب الماء
  Future<void> scheduleWaterReminders({
    required int startHour,
    required int endHour,
    required int intervalHours,
  }) async {
    // إلغاء الإشعارات السابقة
    for (int i = 400; i < 420; i++) {
      await cancelNotification(i);
    }

    // جدولة إشعارات الماء
    int id = 400;
    for (int hour = startHour; hour <= endHour; hour += intervalHours) {
      await scheduleDailyNotification(
        id: id++,
        title: '💧 وقت شرب الماء!',
        body: 'اشرب كوب ماء للحفاظ على ترطيب جسمك',
        hour: hour,
        minute: 0,
        payload: 'water_reminder',
      );
    }
  }

  /// إشعار تحفيزي
  Future<void> scheduleMotivationalNotification() async {
    final motivationalMessages = [
      'أنت أقوى مما تعتقد! استمر في رحلتك الصحية 💪',
      'كل خطوة تقربك من هدفك. لا تستسلم! 🎯',
      'صحتك هي أغلى ما تملك. اعتن بها جيداً ❤️',
      'التقدم البطيء أفضل من عدم التقدم. استمر! 🚀',
      'أنت تستحق أن تكون بأفضل حالاتك! ✨',
    ];

    final random = DateTime.now().millisecond % motivationalMessages.length;
    final message = motivationalMessages[random];

    await showNotification(
      title: '🌟 رسالة تحفيزية',
      body: message,
      payload: 'motivational',
    );
  }

  /// الحصول على FCM Token
  Future<String?> getFCMToken() async {
    return await _firebaseMessaging.getToken();
  }

  /// تحديث FCM Token
  void onTokenRefresh(Function(String) onTokenReceived) {
    _firebaseMessaging.onTokenRefresh.listen(onTokenReceived);
  }
}
