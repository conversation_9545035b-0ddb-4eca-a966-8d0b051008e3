{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App Flutter\\ai_flutter\\sahtak_ai\\android\\app\\.cxx\\Debug\\34305i6x\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App Flutter\\ai_flutter\\sahtak_ai\\android\\app\\.cxx\\Debug\\34305i6x\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}