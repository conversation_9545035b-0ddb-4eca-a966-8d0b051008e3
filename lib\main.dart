import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'utils/constants.dart';
import 'services/auth_service.dart';
import 'services/notification_service.dart';
import 'services/ads_service.dart';
import 'screens/splash_screen.dart';

/// نقطة البداية الرئيسية للتطبيق
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase
  await Firebase.initializeApp();

  // تهيئة خدمة الإشعارات
  await NotificationService().initialize();

  // تهيئة خدمة الإعلانات
  await AdsService().initialize();

  runApp(const SahtakAIApp());
}

/// التطبيق الرئيسي - صحتك AI
class SahtakAIApp extends StatelessWidget {
  const SahtakAIApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // مزود خدمة المصادقة
        Provider<AuthService>(
          create: (_) => AuthService(),
        ),
        // يمكن إضافة مزودين آخرين هنا
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,

        // إعدادات اللغة العربية
        locale: const Locale('ar', 'SA'),
        supportedLocales: const [
          Locale('ar', 'SA'), // العربية
          Locale('en', 'US'), // الإنجليزية
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],

        // ثيم التطبيق
        theme: ThemeData(
          primarySwatch: Colors.green,
          primaryColor: AppConstants.primaryColor,
          scaffoldBackgroundColor: AppConstants.backgroundColor,

          // الخطوط العربية
          textTheme: GoogleFonts.cairoTextTheme(
            Theme.of(context).textTheme,
          ).apply(
            bodyColor: AppConstants.textPrimaryColor,
            displayColor: AppConstants.textPrimaryColor,
          ),

          // شريط التطبيق
          appBarTheme: AppBarTheme(
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            centerTitle: true,
            titleTextStyle: GoogleFonts.cairo(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          // الأزرار المرفوعة
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingMedium,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              textStyle: GoogleFonts.cairo(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // الأزرار النصية
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: AppConstants.primaryColor,
              textStyle: GoogleFonts.cairo(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // البطاقات
          cardTheme: CardTheme(
            color: AppConstants.cardColor,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            margin: const EdgeInsets.all(AppConstants.paddingSmall),
          ),

          // حقول الإدخال
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.errorColor),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
            labelStyle: GoogleFonts.cairo(
              color: AppConstants.textSecondaryColor,
            ),
            hintStyle: GoogleFonts.cairo(
              color: AppConstants.textSecondaryColor,
            ),
          ),

          // شريط التنقل السفلي
          bottomNavigationBarTheme: const BottomNavigationBarThemeData(
            backgroundColor: Colors.white,
            selectedItemColor: AppConstants.primaryColor,
            unselectedItemColor: AppConstants.textSecondaryColor,
            type: BottomNavigationBarType.fixed,
            elevation: 8,
          ),

          // الأيقونات
          iconTheme: const IconThemeData(
            color: AppConstants.primaryColor,
          ),

          // الفواصل
          dividerTheme: const DividerThemeData(
            color: Colors.grey,
            thickness: 1,
          ),
        ),

        // الشاشة الرئيسية
        home: const SplashScreen(),
      ),
    );
  }
}


