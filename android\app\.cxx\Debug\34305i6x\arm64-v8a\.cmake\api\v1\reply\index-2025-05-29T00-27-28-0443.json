{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-c5c495970f041d805498.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-c4dc99c56e0036d1a542.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0730674a5c4ce98aa2d4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-c4dc99c56e0036d1a542.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-0730674a5c4ce98aa2d4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-c5c495970f041d805498.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}