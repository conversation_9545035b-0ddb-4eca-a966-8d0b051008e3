import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../models/workout_model.dart';
import '../models/meal_model.dart';
import '../models/recipe_model.dart';
import '../models/progress_model.dart';

/// خدمة Firebase الأساسية لإدارة البيانات
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _auth.currentUser?.uid;

  // ==================== إدارة المستخدمين ====================

  /// حفظ بيانات المستخدم
  Future<void> saveUserProfile(UserModel user) async {
    try {
      await _firestore
          .collection('users')
          .doc(user.id)
          .set(user.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ بيانات المستخدم: $e');
    }
  }

  /// الحصول على بيانات المستخدم
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب بيانات المستخدم: $e');
    }
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUserProfile(String userId, Map<String, dynamic> updates) async {
    try {
      updates['updatedAt'] = Timestamp.now();
      await _firestore
          .collection('users')
          .doc(userId)
          .update(updates);
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المستخدم: $e');
    }
  }

  // ==================== إدارة خطط التمارين ====================

  /// حفظ خطة التمارين
  Future<void> saveWorkoutPlan(WorkoutPlan plan) async {
    try {
      await _firestore
          .collection('workout_plans')
          .doc(plan.id)
          .set(plan.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ خطة التمارين: $e');
    }
  }

  /// الحصول على خطة التمارين النشطة للمستخدم
  Future<WorkoutPlan?> getActiveWorkoutPlan(String userId) async {
    try {
      final query = await _firestore
          .collection('workout_plans')
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return WorkoutPlan.fromMap(query.docs.first.data());
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب خطة التمارين: $e');
    }
  }

  /// الحصول على جميع خطط التمارين للمستخدم
  Future<List<WorkoutPlan>> getUserWorkoutPlans(String userId) async {
    try {
      final query = await _firestore
          .collection('workout_plans')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => WorkoutPlan.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب خطط التمارين: $e');
    }
  }

  // ==================== إدارة خطط الوجبات ====================

  /// حفظ خطة الوجبات اليومية
  Future<void> saveDailyMealPlan(DailyMealPlan plan) async {
    try {
      await _firestore
          .collection('meal_plans')
          .doc(plan.id)
          .set(plan.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ خطة الوجبات: $e');
    }
  }

  /// الحصول على خطة الوجبات لتاريخ معين
  Future<DailyMealPlan?> getDailyMealPlan(String userId, DateTime date) async {
    try {
      final query = await _firestore
          .collection('meal_plans')
          .where('userId', isEqualTo: userId)
          .where('date', isEqualTo: Timestamp.fromDate(DateTime(date.year, date.month, date.day)))
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return DailyMealPlan.fromMap(query.docs.first.data());
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب خطة الوجبات: $e');
    }
  }

  /// الحصول على خطط الوجبات لفترة معينة
  Future<List<DailyMealPlan>> getMealPlansForPeriod(
    String userId,
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final query = await _firestore
          .collection('meal_plans')
          .where('userId', isEqualTo: userId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date')
          .get();

      return query.docs
          .map((doc) => DailyMealPlan.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب خطط الوجبات: $e');
    }
  }

  // ==================== إدارة الوصفات ====================

  /// الحصول على الوصفات المميزة
  Future<List<Recipe>> getFeaturedRecipes({int limit = 10}) async {
    try {
      final query = await _firestore
          .collection('recipes')
          .where('isFeatured', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(limit)
          .get();

      return query.docs
          .map((doc) => Recipe.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الوصفات المميزة: $e');
    }
  }

  /// البحث في الوصفات
  Future<List<Recipe>> searchRecipes({
    String? query,
    String? category,
    String? dietType,
    String? difficulty,
    int limit = 20,
  }) async {
    try {
      Query<Map<String, dynamic>> firestoreQuery = _firestore.collection('recipes');

      if (category != null && category.isNotEmpty) {
        firestoreQuery = firestoreQuery.where('category', isEqualTo: category);
      }

      if (dietType != null && dietType.isNotEmpty) {
        firestoreQuery = firestoreQuery.where('dietTypes', arrayContains: dietType);
      }

      if (difficulty != null && difficulty.isNotEmpty) {
        firestoreQuery = firestoreQuery.where('difficulty', isEqualTo: difficulty);
      }

      firestoreQuery = firestoreQuery
          .orderBy('rating', descending: true)
          .limit(limit);

      final result = await firestoreQuery.get();

      List<Recipe> recipes = result.docs
          .map((doc) => Recipe.fromMap(doc.data()))
          .toList();

      // تطبيق البحث النصي محلياً
      if (query != null && query.isNotEmpty) {
        recipes = recipes.where((recipe) =>
          recipe.name.toLowerCase().contains(query.toLowerCase()) ||
          recipe.description.toLowerCase().contains(query.toLowerCase()) ||
          recipe.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()))
        ).toList();
      }

      return recipes;
    } catch (e) {
      throw Exception('فشل في البحث عن الوصفات: $e');
    }
  }

  /// الحصول على وصفة بالمعرف
  Future<Recipe?> getRecipeById(String recipeId) async {
    try {
      final doc = await _firestore
          .collection('recipes')
          .doc(recipeId)
          .get();

      if (doc.exists) {
        return Recipe.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب الوصفة: $e');
    }
  }

  // ==================== إدارة المفضلة ====================

  /// إضافة وصفة للمفضلة
  Future<void> addToFavorites(String userId, String recipeId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(recipeId)
          .set({
        'recipeId': recipeId,
        'addedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('فشل في إضافة الوصفة للمفضلة: $e');
    }
  }

  /// إزالة وصفة من المفضلة
  Future<void> removeFromFavorites(String userId, String recipeId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(recipeId)
          .delete();
    } catch (e) {
      throw Exception('فشل في إزالة الوصفة من المفضلة: $e');
    }
  }

  /// الحصول على الوصفات المفضلة
  Future<List<Recipe>> getFavoriteRecipes(String userId) async {
    try {
      final favoritesQuery = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();

      if (favoritesQuery.docs.isEmpty) {
        return [];
      }

      final recipeIds = favoritesQuery.docs
          .map((doc) => doc.data()['recipeId'] as String)
          .toList();

      // جلب الوصفات بناءً على المعرفات
      final recipes = <Recipe>[];
      for (final recipeId in recipeIds) {
        final recipe = await getRecipeById(recipeId);
        if (recipe != null) {
          recipes.add(recipe);
        }
      }

      return recipes;
    } catch (e) {
      throw Exception('فشل في جلب الوصفات المفضلة: $e');
    }
  }

  /// التحقق من كون الوصفة في المفضلة
  Future<bool> isRecipeFavorite(String userId, String recipeId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(recipeId)
          .get();

      return doc.exists;
    } catch (e) {
      return false;
    }
  }

  // ==================== إدارة التقدم ====================

  /// حفظ قياس الوزن
  Future<void> saveWeightEntry(WeightEntry entry) async {
    try {
      await _firestore
          .collection('weight_entries')
          .doc(entry.id)
          .set(entry.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ قياس الوزن: $e');
    }
  }

  /// الحصول على قياسات الوزن للمستخدم
  Future<List<WeightEntry>> getWeightEntries(String userId, {int? limit}) async {
    try {
      Query<Map<String, dynamic>> query = _firestore
          .collection('weight_entries')
          .where('userId', isEqualTo: userId)
          .orderBy('date', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final result = await query.get();
      return result.docs
          .map((doc) => WeightEntry.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب قياسات الوزن: $e');
    }
  }

  /// حفظ سجل التمرين
  Future<void> saveWorkoutLog(WorkoutLog log) async {
    try {
      await _firestore
          .collection('workout_logs')
          .doc(log.id)
          .set(log.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ سجل التمرين: $e');
    }
  }

  /// الحصول على سجلات التمارين للمستخدم
  Future<List<WorkoutLog>> getWorkoutLogs(String userId, {int? limit}) async {
    try {
      Query<Map<String, dynamic>> query = _firestore
          .collection('workout_logs')
          .where('userId', isEqualTo: userId)
          .orderBy('date', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final result = await query.get();
      return result.docs
          .map((doc) => WorkoutLog.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب سجلات التمارين: $e');
    }
  }

  /// الحصول على إحصائيات التقدم
  Future<ProgressStats?> getProgressStats(String userId) async {
    try {
      // جلب قياسات الوزن
      final weightEntries = await getWeightEntries(userId);
      if (weightEntries.isEmpty) return null;

      // جلب سجلات التمارين
      final workoutLogs = await getWorkoutLogs(userId);

      // جلب بيانات المستخدم
      final userProfile = await getUserProfile(userId);
      if (userProfile == null) return null;

      // حساب الإحصائيات
      final currentWeight = weightEntries.first.weight;
      final startWeight = weightEntries.last.weight;
      final targetWeight = userProfile.targetWeight;
      final weightLost = startWeight - currentWeight;
      final weightToGo = currentWeight - targetWeight;

      final totalWorkouts = workoutLogs.length;
      final totalCaloriesBurned = workoutLogs.fold(0, (sum, log) => sum + log.caloriesBurned);
      final averageWorkoutDuration = totalWorkouts > 0
          ? workoutLogs.fold(0, (sum, log) => sum + log.duration) ~/ totalWorkouts
          : 0;

      return ProgressStats(
        currentWeight: currentWeight,
        targetWeight: targetWeight,
        startWeight: startWeight,
        weightLost: weightLost,
        weightToGo: weightToGo,
        totalWorkouts: totalWorkouts,
        totalCaloriesBurned: totalCaloriesBurned,
        averageWorkoutDuration: averageWorkoutDuration,
        lastWeightEntry: weightEntries.first.date,
        lastWorkout: workoutLogs.isNotEmpty ? workoutLogs.first.date : DateTime.now(),
      );
    } catch (e) {
      throw Exception('فشل في حساب إحصائيات التقدم: $e');
    }
  }

  /// الحصول على بيانات الرسم البياني
  Future<ChartData> getChartData(String userId, DateTime startDate, DateTime endDate) async {
    try {
      // جلب قياسات الوزن في الفترة المحددة
      final weightQuery = await _firestore
          .collection('weight_entries')
          .where('userId', isEqualTo: userId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date')
          .get();

      final weightData = weightQuery.docs
          .map((doc) => WeightEntry.fromMap(doc.data()))
          .map((entry) => ChartDataPoint(
                date: entry.date,
                value: entry.weight,
                label: '${entry.weight.toStringAsFixed(1)} كجم',
              ))
          .toList();

      // جلب سجلات التمارين في الفترة المحددة
      final workoutQuery = await _firestore
          .collection('workout_logs')
          .where('userId', isEqualTo: userId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date')
          .get();

      final workoutLogs = workoutQuery.docs
          .map((doc) => WorkoutLog.fromMap(doc.data()))
          .toList();

      // تجميع البيانات حسب اليوم
      final Map<String, double> dailyCalories = {};
      final Map<String, double> dailyWorkoutMinutes = {};

      for (final log in workoutLogs) {
        final dateKey = '${log.date.year}-${log.date.month}-${log.date.day}';
        dailyCalories[dateKey] = (dailyCalories[dateKey] ?? 0) + log.caloriesBurned;
        dailyWorkoutMinutes[dateKey] = (dailyWorkoutMinutes[dateKey] ?? 0) + log.duration;
      }

      final caloriesData = dailyCalories.entries
          .map((entry) => ChartDataPoint(
                date: DateTime.parse(entry.key.replaceAll('-', '-')),
                value: entry.value,
                label: '${entry.value.toStringAsFixed(0)} سعرة',
              ))
          .toList();

      final workoutData = dailyWorkoutMinutes.entries
          .map((entry) => ChartDataPoint(
                date: DateTime.parse(entry.key.replaceAll('-', '-')),
                value: entry.value,
                label: '${entry.value.toStringAsFixed(0)} دقيقة',
              ))
          .toList();

      return ChartData(
        weightData: weightData,
        caloriesData: caloriesData,
        workoutData: workoutData,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      throw Exception('فشل في جلب بيانات الرسم البياني: $e');
    }
  }
}
