import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج المكونات الغذائية
class NutritionInfo {
  final int calories;
  final double protein; // بالجرام
  final double carbs; // بالجرام
  final double fat; // بالجرام
  final double fiber; // بالجرام
  final double sugar; // بالجرام
  final int sodium; // بالميليجرام

  NutritionInfo({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
    required this.fiber,
    required this.sugar,
    required this.sodium,
  });

  Map<String, dynamic> toMap() {
    return {
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
    };
  }

  factory NutritionInfo.fromMap(Map<String, dynamic> map) {
    return NutritionInfo(
      calories: map['calories'] ?? 0,
      protein: (map['protein'] ?? 0).toDouble(),
      carbs: (map['carbs'] ?? 0).toDouble(),
      fat: (map['fat'] ?? 0).toDouble(),
      fiber: (map['fiber'] ?? 0).toDouble(),
      sugar: (map['sugar'] ?? 0).toDouble(),
      sodium: map['sodium'] ?? 0,
    );
  }
}

/// نموذج المكون الواحد
class Ingredient {
  final String name;
  final double amount; // الكمية
  final String unit; // الوحدة (جرام، كوب، ملعقة)
  final String? notes; // ملاحظات إضافية

  Ingredient({
    required this.name,
    required this.amount,
    required this.unit,
    this.notes,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'amount': amount,
      'unit': unit,
      'notes': notes,
    };
  }

  factory Ingredient.fromMap(Map<String, dynamic> map) {
    return Ingredient(
      name: map['name'] ?? '',
      amount: (map['amount'] ?? 0).toDouble(),
      unit: map['unit'] ?? '',
      notes: map['notes'],
    );
  }
}

/// نموذج الوجبة
class Meal {
  final String id;
  final String name;
  final String description;
  final String type; // إفطار، غداء، عشاء، سناك
  final List<Ingredient> ingredients;
  final List<String> instructions; // خطوات التحضير
  final NutritionInfo nutrition;
  final int preparationTime; // وقت التحضير بالدقائق
  final int cookingTime; // وقت الطبخ بالدقائق
  final int servings; // عدد الحصص
  final String difficulty; // سهل، متوسط، صعب
  final List<String> dietTypes; // نباتي، كيتو، إلخ
  final List<String> allergens; // مسببات الحساسية
  final String? imageUrl;
  final bool isFavorite;
  final DateTime createdAt;

  Meal({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.ingredients,
    required this.instructions,
    required this.nutrition,
    required this.preparationTime,
    required this.cookingTime,
    required this.servings,
    required this.difficulty,
    required this.dietTypes,
    required this.allergens,
    this.imageUrl,
    this.isFavorite = false,
    required this.createdAt,
  });

  /// إجمالي وقت التحضير والطبخ
  int get totalTime => preparationTime + cookingTime;

  /// السعرات لكل حصة
  int get caloriesPerServing => (nutrition.calories / servings).round();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'ingredients': ingredients.map((i) => i.toMap()).toList(),
      'instructions': instructions,
      'nutrition': nutrition.toMap(),
      'preparationTime': preparationTime,
      'cookingTime': cookingTime,
      'servings': servings,
      'difficulty': difficulty,
      'dietTypes': dietTypes,
      'allergens': allergens,
      'imageUrl': imageUrl,
      'isFavorite': isFavorite,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory Meal.fromMap(Map<String, dynamic> map) {
    return Meal(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: map['type'] ?? '',
      ingredients: (map['ingredients'] as List<dynamic>?)
          ?.map((i) => Ingredient.fromMap(i as Map<String, dynamic>))
          .toList() ?? [],
      instructions: List<String>.from(map['instructions'] ?? []),
      nutrition: NutritionInfo.fromMap(map['nutrition'] ?? {}),
      preparationTime: map['preparationTime'] ?? 0,
      cookingTime: map['cookingTime'] ?? 0,
      servings: map['servings'] ?? 1,
      difficulty: map['difficulty'] ?? '',
      dietTypes: List<String>.from(map['dietTypes'] ?? []),
      allergens: List<String>.from(map['allergens'] ?? []),
      imageUrl: map['imageUrl'],
      isFavorite: map['isFavorite'] ?? false,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }

  Meal copyWith({
    String? id,
    String? name,
    String? description,
    String? type,
    List<Ingredient>? ingredients,
    List<String>? instructions,
    NutritionInfo? nutrition,
    int? preparationTime,
    int? cookingTime,
    int? servings,
    String? difficulty,
    List<String>? dietTypes,
    List<String>? allergens,
    String? imageUrl,
    bool? isFavorite,
    DateTime? createdAt,
  }) {
    return Meal(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      nutrition: nutrition ?? this.nutrition,
      preparationTime: preparationTime ?? this.preparationTime,
      cookingTime: cookingTime ?? this.cookingTime,
      servings: servings ?? this.servings,
      difficulty: difficulty ?? this.difficulty,
      dietTypes: dietTypes ?? this.dietTypes,
      allergens: allergens ?? this.allergens,
      imageUrl: imageUrl ?? this.imageUrl,
      isFavorite: isFavorite ?? this.isFavorite,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// نموذج خطة الوجبات اليومية
class DailyMealPlan {
  final String id;
  final String userId;
  final DateTime date;
  final Map<String, Meal?> meals; // نوع الوجبة -> الوجبة
  final int totalCalories;
  final NutritionInfo totalNutrition;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  DailyMealPlan({
    required this.id,
    required this.userId,
    required this.date,
    required this.meals,
    required this.totalCalories,
    required this.totalNutrition,
    required this.isCompleted,
    required this.createdAt,
    required this.updatedAt,
  });

  /// الحصول على وجبة معينة
  Meal? getMeal(String mealType) {
    return meals[mealType];
  }

  /// التحقق من وجود وجبة معينة
  bool hasMeal(String mealType) {
    return meals[mealType] != null;
  }

  /// عدد الوجبات المخططة
  int get plannedMealsCount {
    return meals.values.where((meal) => meal != null).length;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'date': Timestamp.fromDate(date),
      'meals': meals.map((key, value) => MapEntry(key, value?.toMap())),
      'totalCalories': totalCalories,
      'totalNutrition': totalNutrition.toMap(),
      'isCompleted': isCompleted,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory DailyMealPlan.fromMap(Map<String, dynamic> map) {
    return DailyMealPlan(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      date: (map['date'] as Timestamp).toDate(),
      meals: (map['meals'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(
          key,
          value != null ? Meal.fromMap(value as Map<String, dynamic>) : null,
        ),
      ) ?? {},
      totalCalories: map['totalCalories'] ?? 0,
      totalNutrition: NutritionInfo.fromMap(map['totalNutrition'] ?? {}),
      isCompleted: map['isCompleted'] ?? false,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  DailyMealPlan copyWith({
    String? id,
    String? userId,
    DateTime? date,
    Map<String, Meal?>? meals,
    int? totalCalories,
    NutritionInfo? totalNutrition,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DailyMealPlan(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      meals: meals ?? this.meals,
      totalCalories: totalCalories ?? this.totalCalories,
      totalNutrition: totalNutrition ?? this.totalNutrition,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
