import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// مساعدات وأدوات عامة للتطبيق
class AppHelpers {
  
  /// تنسيق التاريخ باللغة العربية
  static String formatDate(DateTime date) {
    final formatter = DateFormat('dd/MM/yyyy', 'ar');
    return formatter.format(date);
  }

  /// تنسيق الوقت باللغة العربية
  static String formatTime(DateTime time) {
    final formatter = DateFormat('HH:mm', 'ar');
    return formatter.format(time);
  }

  /// تنسيق التاريخ والوقت معاً
  static String formatDateTime(DateTime dateTime) {
    final formatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');
    return formatter.format(dateTime);
  }

  /// حساب العمر من تاريخ الميلاد
  static int calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// حساب مؤشر كتلة الجسم (BMI)
  static double calculateBMI(double weight, double height) {
    // الطول بالمتر
    final heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  }

  /// تفسير مؤشر كتلة الجسم
  static String interpretBMI(double bmi) {
    if (bmi < 18.5) {
      return 'نقص في الوزن';
    } else if (bmi < 25) {
      return 'وزن طبيعي';
    } else if (bmi < 30) {
      return 'زيادة في الوزن';
    } else {
      return 'سمنة';
    }
  }

  /// حساب السعرات الحرارية المطلوبة يومياً
  static int calculateDailyCalories({
    required double weight,
    required double height,
    required int age,
    required String gender,
    required String activityLevel,
  }) {
    // معادلة Harris-Benedict المحدثة
    double bmr;
    if (gender == 'ذكر') {
      bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
    } else {
      bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
    }

    // ضرب معامل النشاط
    double activityFactor;
    switch (activityLevel) {
      case 'منخفض (قليل الحركة)':
        activityFactor = 1.2;
        break;
      case 'متوسط (تمارين خفيفة)':
        activityFactor = 1.375;
        break;
      case 'مرتفع (تمارين منتظمة)':
        activityFactor = 1.55;
        break;
      case 'عالي جداً (رياضي)':
        activityFactor = 1.725;
        break;
      default:
        activityFactor = 1.2;
    }

    return (bmr * activityFactor).round();
  }

  /// تحويل الوزن من كيلو إلى رطل
  static double kgToPounds(double kg) {
    return kg * 2.20462;
  }

  /// تحويل الطول من سم إلى قدم وبوصة
  static String cmToFeetInches(double cm) {
    final totalInches = cm / 2.54;
    final feet = (totalInches / 12).floor();
    final inches = (totalInches % 12).round();
    return '$feet\' $inches"';
  }

  /// عرض رسالة نجاح
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// عرض رسالة خطأ
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// عرض حوار تأكيد
  static Future<bool?> showConfirmDialog(
    BuildContext context,
    String title,
    String content,
  ) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// عرض مؤشر التحميل
  static void showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// إخفاء مؤشر التحميل
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من قوة كلمة المرور
  static bool isStrongPassword(String password) {
    // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم
    return password.length >= 8 &&
           RegExp(r'[A-Z]').hasMatch(password) &&
           RegExp(r'[a-z]').hasMatch(password) &&
           RegExp(r'[0-9]').hasMatch(password);
  }

  /// تنظيف النص من المسافات الزائدة
  static String cleanText(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// تحويل النص إلى عنوان (أول حرف كبير)
  static String toTitleCase(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// تحديد لون النص حسب الخلفية
  static Color getTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// تحويل الثواني إلى تنسيق mm:ss
  static String formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// حساب النسبة المئوية
  static double calculatePercentage(double current, double target) {
    if (target == 0) return 0;
    return (current / target) * 100;
  }

  /// تقريب الرقم إلى منزلة عشرية واحدة
  static double roundToOneDecimal(double value) {
    return double.parse(value.toStringAsFixed(1));
  }

  /// تحويل الرقم إلى نص مع فاصلة الآلاف
  static String formatNumber(int number) {
    final formatter = NumberFormat('#,###', 'ar');
    return formatter.format(number);
  }

  /// الحصول على تحية حسب الوقت
  static String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  }

  /// تحديد أيقونة الطقس حسب الوقت
  static IconData getTimeIcon() {
    final hour = DateTime.now().hour;
    if (hour >= 6 && hour < 18) {
      return Icons.wb_sunny;
    } else {
      return Icons.nights_stay;
    }
  }
}
