import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات المستخدم
class UserModel {
  final String id;
  final String name;
  final String email;
  final String? photoUrl;
  final String gender; // ذكر / أنثى
  final DateTime birthDate;
  final double height; // بالسنتيمتر
  final double currentWeight; // بالكيلوجرام
  final double targetWeight; // بالكيلوجرام
  final String goal; // الهدف: خسارة وزن، بناء عضل، لياقة عامة
  final String activityLevel; // مستوى النشاط
  final String dietType; // نوع الدايت
  final List<String> allergies; // الحساسيات الغذائية
  final int mealsPerDay; // عدد الوجبات اليومية
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.photoUrl,
    required this.gender,
    required this.birthDate,
    required this.height,
    required this.currentWeight,
    required this.targetWeight,
    required this.goal,
    required this.activityLevel,
    required this.dietType,
    required this.allergies,
    required this.mealsPerDay,
    required this.createdAt,
    required this.updatedAt,
  });

  /// حساب العمر
  int get age {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// حساب مؤشر كتلة الجسم
  double get bmi {
    final heightInMeters = height / 100;
    return currentWeight / (heightInMeters * heightInMeters);
  }

  /// تفسير مؤشر كتلة الجسم
  String get bmiCategory {
    if (bmi < 18.5) {
      return 'نقص في الوزن';
    } else if (bmi < 25) {
      return 'وزن طبيعي';
    } else if (bmi < 30) {
      return 'زيادة في الوزن';
    } else {
      return 'سمنة';
    }
  }

  /// حساب السعرات الحرارية المطلوبة يومياً
  int get dailyCalories {
    // معادلة Harris-Benedict المحدثة
    double bmr;
    if (gender == 'ذكر') {
      bmr = 88.362 + (13.397 * currentWeight) + (4.799 * height) - (5.677 * age);
    } else {
      bmr = 447.593 + (9.247 * currentWeight) + (3.098 * height) - (4.330 * age);
    }

    // ضرب معامل النشاط
    double activityFactor;
    switch (activityLevel) {
      case 'منخفض (قليل الحركة)':
        activityFactor = 1.2;
        break;
      case 'متوسط (تمارين خفيفة)':
        activityFactor = 1.375;
        break;
      case 'مرتفع (تمارين منتظمة)':
        activityFactor = 1.55;
        break;
      case 'عالي جداً (رياضي)':
        activityFactor = 1.725;
        break;
      default:
        activityFactor = 1.2;
    }

    return (bmr * activityFactor).round();
  }

  /// تحويل إلى Map للحفظ في Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'photoUrl': photoUrl,
      'gender': gender,
      'birthDate': Timestamp.fromDate(birthDate),
      'height': height,
      'currentWeight': currentWeight,
      'targetWeight': targetWeight,
      'goal': goal,
      'activityLevel': activityLevel,
      'dietType': dietType,
      'allergies': allergies,
      'mealsPerDay': mealsPerDay,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// إنشاء من Map (من Firebase)
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      photoUrl: map['photoUrl'],
      gender: map['gender'] ?? '',
      birthDate: (map['birthDate'] as Timestamp).toDate(),
      height: (map['height'] ?? 0).toDouble(),
      currentWeight: (map['currentWeight'] ?? 0).toDouble(),
      targetWeight: (map['targetWeight'] ?? 0).toDouble(),
      goal: map['goal'] ?? '',
      activityLevel: map['activityLevel'] ?? '',
      dietType: map['dietType'] ?? '',
      allergies: List<String>.from(map['allergies'] ?? []),
      mealsPerDay: map['mealsPerDay'] ?? 3,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  /// إنشاء نسخة محدثة
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? photoUrl,
    String? gender,
    DateTime? birthDate,
    double? height,
    double? currentWeight,
    double? targetWeight,
    String? goal,
    String? activityLevel,
    String? dietType,
    List<String>? allergies,
    int? mealsPerDay,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      photoUrl: photoUrl ?? this.photoUrl,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      height: height ?? this.height,
      currentWeight: currentWeight ?? this.currentWeight,
      targetWeight: targetWeight ?? this.targetWeight,
      goal: goal ?? this.goal,
      activityLevel: activityLevel ?? this.activityLevel,
      dietType: dietType ?? this.dietType,
      allergies: allergies ?? this.allergies,
      mealsPerDay: mealsPerDay ?? this.mealsPerDay,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, gender: $gender, age: $age, height: $height, currentWeight: $currentWeight, targetWeight: $targetWeight, goal: $goal, activityLevel: $activityLevel, dietType: $dietType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
