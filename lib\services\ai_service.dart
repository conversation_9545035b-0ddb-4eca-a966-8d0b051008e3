import 'dart:math';
import '../models/user_model.dart';
import '../models/workout_model.dart';
import '../models/meal_model.dart';

/// خدمة الذكاء الاصطناعي لتوليد الخطط المخصصة
class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  final Random _random = Random();

  // ==================== توليد خطط التمارين ====================

  /// توليد خطة تمارين مخصصة للمستخدم
  Future<WorkoutPlan> generateWorkoutPlan(UserModel user) async {
    // محاكاة وقت المعالجة
    await Future.delayed(const Duration(seconds: 2));

    final workoutDays = _getWorkoutDaysBasedOnGoal(user.goal, user.activityLevel);
    final difficulty = _getDifficultyLevel(user.activityLevel);
    
    final weeklySchedule = <String, WorkoutSession?>{
      'الاثنين': null,
      'الثلاثاء': null,
      'الأربعاء': null,
      'الخميس': null,
      'الجمعة': null,
      'السبت': null,
      'الأحد': null,
    };

    // توزيع التمارين على الأسبوع
    final days = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'];
    final selectedDays = _selectWorkoutDays(days, workoutDays);

    for (int i = 0; i < selectedDays.length; i++) {
      final day = selectedDays[i];
      final workoutType = _getWorkoutTypeForDay(i, user.goal);
      final session = _generateWorkoutSession(
        day: day,
        type: workoutType,
        difficulty: difficulty,
        userGoal: user.goal,
        duration: _getWorkoutDuration(user.activityLevel),
      );
      weeklySchedule[day] = session;
    }

    return WorkoutPlan(
      id: 'plan_${DateTime.now().millisecondsSinceEpoch}',
      userId: user.id,
      name: 'خطة ${user.goal} المخصصة',
      description: _generatePlanDescription(user),
      weeklySchedule: weeklySchedule,
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 28)), // 4 أسابيع
      goal: user.goal,
      difficulty: difficulty,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// توليد جلسة تمرين واحدة
  WorkoutSession _generateWorkoutSession({
    required String day,
    required String type,
    required String difficulty,
    required String userGoal,
    required int duration,
  }) {
    final exercises = _generateExercises(type, difficulty, duration);
    final totalCalories = exercises.fold(0, (sum, exercise) => sum + exercise.caloriesBurned);

    return WorkoutSession(
      id: 'session_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(1000)}',
      name: 'تمرين $type - $day',
      description: _getWorkoutDescription(type, userGoal),
      exercises: exercises,
      totalDuration: duration,
      totalCalories: totalCalories,
      difficulty: difficulty,
      type: type,
      createdAt: DateTime.now(),
    );
  }

  /// توليد قائمة التمارين
  List<Exercise> _generateExercises(String type, String difficulty, int totalDuration) {
    final exercises = <Exercise>[];
    int remainingTime = totalDuration;

    final exerciseTemplates = _getExerciseTemplates(type);
    final selectedTemplates = exerciseTemplates.take(4).toList();

    for (final template in selectedTemplates) {
      if (remainingTime <= 0) break;

      final duration = (remainingTime / selectedTemplates.length).round();
      final exercise = _createExerciseFromTemplate(template, difficulty, duration);
      exercises.add(exercise);
      remainingTime -= duration;
    }

    return exercises;
  }

  // ==================== توليد خطط الوجبات ====================

  /// توليد خطة وجبات يومية
  Future<DailyMealPlan> generateDailyMealPlan(UserModel user, DateTime date) async {
    // محاكاة وقت المعالجة
    await Future.delayed(const Duration(seconds: 1));

    final targetCalories = user.dailyCalories;
    final meals = <String, Meal?>{};

    // توزيع السعرات على الوجبات
    final calorieDistribution = _getCalorieDistribution(user.mealsPerDay);
    
    if (user.mealsPerDay >= 3) {
      meals['الإفطار'] = _generateMeal('الإفطار', (targetCalories * calorieDistribution['الإفطار']!).round(), user);
      meals['الغداء'] = _generateMeal('الغداء', (targetCalories * calorieDistribution['الغداء']!).round(), user);
      meals['العشاء'] = _generateMeal('العشاء', (targetCalories * calorieDistribution['العشاء']!).round(), user);
    }

    if (user.mealsPerDay >= 4) {
      meals['سناك صباحي'] = _generateMeal('سناك صباحي', (targetCalories * 0.1).round(), user);
    }

    if (user.mealsPerDay >= 5) {
      meals['سناك مسائي'] = _generateMeal('سناك مسائي', (targetCalories * 0.1).round(), user);
    }

    final totalCalories = meals.values
        .where((meal) => meal != null)
        .fold(0, (sum, meal) => sum + meal!.nutrition.calories);

    final totalNutrition = _calculateTotalNutrition(meals.values.where((m) => m != null).cast<Meal>().toList());

    return DailyMealPlan(
      id: 'meal_plan_${date.millisecondsSinceEpoch}',
      userId: user.id,
      date: date,
      meals: meals,
      totalCalories: totalCalories,
      totalNutrition: totalNutrition,
      isCompleted: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// توليد وجبة واحدة
  Meal _generateMeal(String type, int targetCalories, UserModel user) {
    final mealTemplates = _getMealTemplates(type, user.dietType);
    final template = mealTemplates[_random.nextInt(mealTemplates.length)];
    
    return Meal(
      id: 'meal_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(1000)}',
      name: template['name'],
      description: template['description'],
      type: type,
      ingredients: _generateIngredients(template['ingredients']),
      instructions: List<String>.from(template['instructions']),
      nutrition: _adjustNutritionToCalories(template['nutrition'], targetCalories),
      preparationTime: template['preparationTime'],
      cookingTime: template['cookingTime'],
      servings: 1,
      difficulty: template['difficulty'],
      dietTypes: [user.dietType],
      allergens: _filterAllergens(template['allergens'], user.allergies),
      createdAt: DateTime.now(),
    );
  }

  // ==================== الدوال المساعدة ====================

  int _getWorkoutDaysBasedOnGoal(String goal, String activityLevel) {
    switch (goal) {
      case 'خسارة الوزن':
        return activityLevel == 'مرتفع (تمارين منتظمة)' ? 5 : 4;
      case 'بناء العضلات':
        return 4;
      case 'اللياقة العامة':
        return 3;
      default:
        return 3;
    }
  }

  String _getDifficultyLevel(String activityLevel) {
    switch (activityLevel) {
      case 'منخفض (قليل الحركة)':
        return 'سهل';
      case 'متوسط (تمارين خفيفة)':
        return 'متوسط';
      case 'مرتفع (تمارين منتظمة)':
        return 'متوسط';
      case 'عالي جداً (رياضي)':
        return 'صعب';
      default:
        return 'متوسط';
    }
  }

  int _getWorkoutDuration(String activityLevel) {
    switch (activityLevel) {
      case 'منخفض (قليل الحركة)':
        return 20;
      case 'متوسط (تمارين خفيفة)':
        return 30;
      case 'مرتفع (تمارين منتظمة)':
        return 45;
      case 'عالي جداً (رياضي)':
        return 60;
      default:
        return 30;
    }
  }

  List<String> _selectWorkoutDays(List<String> allDays, int workoutDays) {
    final selected = <String>[];
    final availableDays = List<String>.from(allDays);
    
    for (int i = 0; i < workoutDays && availableDays.isNotEmpty; i++) {
      final index = _random.nextInt(availableDays.length);
      selected.add(availableDays.removeAt(index));
    }
    
    return selected;
  }

  String _getWorkoutTypeForDay(int dayIndex, String goal) {
    final types = ['كارديو', 'قوة', 'مرونة'];
    if (goal == 'خسارة الوزن') {
      return dayIndex % 2 == 0 ? 'كارديو' : 'قوة';
    } else if (goal == 'بناء العضلات') {
      return dayIndex % 3 == 0 ? 'قوة' : (dayIndex % 3 == 1 ? 'كارديو' : 'مرونة');
    }
    return types[dayIndex % types.length];
  }

  String _generatePlanDescription(UserModel user) {
    return 'خطة تمارين مخصصة لـ${user.goal} تناسب مستوى نشاطك ${user.activityLevel}. '
           'تم تصميم هذه الخطة خصيصاً لك بناءً على بياناتك الشخصية وأهدافك.';
  }

  String _getWorkoutDescription(String type, String goal) {
    switch (type) {
      case 'كارديو':
        return 'تمارين القلب والأوعية الدموية لحرق السعرات وتحسين اللياقة';
      case 'قوة':
        return 'تمارين المقاومة لبناء العضلات وزيادة القوة';
      case 'مرونة':
        return 'تمارين الإطالة واليوجا لتحسين المرونة والاسترخاء';
      default:
        return 'تمرين متنوع لتحسين اللياقة العامة';
    }
  }

  List<Map<String, dynamic>> _getExerciseTemplates(String type) {
    // قوائم التمارين المبسطة - يمكن توسيعها لاحقاً
    switch (type) {
      case 'كارديو':
        return [
          {'name': 'المشي السريع', 'description': 'مشي بوتيرة سريعة', 'calories': 5},
          {'name': 'الجري في المكان', 'description': 'جري خفيف في المكان', 'calories': 8},
          {'name': 'القفز بالحبل', 'description': 'قفز بالحبل لمدة محددة', 'calories': 10},
          {'name': 'تمرين الدراجة', 'description': 'ركوب الدراجة الثابتة', 'calories': 7},
        ];
      case 'قوة':
        return [
          {'name': 'تمرين الضغط', 'description': 'تمرين الضغط الكلاسيكي', 'calories': 6},
          {'name': 'تمرين القرفصاء', 'description': 'تمرين القرفصاء للساقين', 'calories': 5},
          {'name': 'تمرين البطن', 'description': 'تمارين عضلات البطن', 'calories': 4},
          {'name': 'تمرين العقلة', 'description': 'تمرين العقلة للظهر', 'calories': 7},
        ];
      case 'مرونة':
        return [
          {'name': 'إطالة الساقين', 'description': 'تمارين إطالة عضلات الساقين', 'calories': 2},
          {'name': 'إطالة الظهر', 'description': 'تمارين إطالة عضلات الظهر', 'calories': 2},
          {'name': 'تمارين اليوجا', 'description': 'وضعيات يوجا أساسية', 'calories': 3},
          {'name': 'إطالة الذراعين', 'description': 'تمارين إطالة عضلات الذراعين', 'calories': 2},
        ];
      default:
        return [];
    }
  }

  Exercise _createExerciseFromTemplate(Map<String, dynamic> template, String difficulty, int duration) {
    final baseCalories = template['calories'] as int;
    final multiplier = difficulty == 'سهل' ? 0.8 : (difficulty == 'صعب' ? 1.2 : 1.0);
    
    return Exercise(
      name: template['name'],
      description: template['description'],
      type: 'عام',
      duration: duration,
      difficulty: difficulty,
      caloriesBurned: (baseCalories * duration * multiplier).round(),
    );
  }

  Map<String, double> _getCalorieDistribution(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return {'الإفطار': 0.25, 'الغداء': 0.45, 'العشاء': 0.30};
      case 4:
        return {'الإفطار': 0.25, 'سناك صباحي': 0.10, 'الغداء': 0.40, 'العشاء': 0.25};
      case 5:
        return {'الإفطار': 0.20, 'سناك صباحي': 0.10, 'الغداء': 0.35, 'سناك مسائي': 0.10, 'العشاء': 0.25};
      default:
        return {'الإفطار': 0.25, 'الغداء': 0.45, 'العشاء': 0.30};
    }
  }

  List<Map<String, dynamic>> _getMealTemplates(String type, String dietType) {
    // قوائم الوجبات المبسطة - يمكن توسيعها لاحقاً
    final templates = <Map<String, dynamic>>[];
    
    switch (type) {
      case 'الإفطار':
        templates.addAll([
          {
            'name': 'شوفان بالفواكه',
            'description': 'شوفان صحي مع الفواكه الطازجة',
            'ingredients': ['شوفان', 'حليب', 'موز', 'عسل'],
            'instructions': ['اخلط الشوفان مع الحليب', 'أضف الفواكه والعسل'],
            'nutrition': {'calories': 300, 'protein': 12, 'carbs': 45, 'fat': 8},
            'preparationTime': 5,
            'cookingTime': 5,
            'difficulty': 'سهل',
            'allergens': [],
          },
        ]);
        break;
      case 'الغداء':
        templates.addAll([
          {
            'name': 'سلطة الدجاج',
            'description': 'سلطة صحية مع قطع الدجاج المشوي',
            'ingredients': ['دجاج مشوي', 'خس', 'طماطم', 'خيار', 'زيت زيتون'],
            'instructions': ['اشوي الدجاج', 'قطع الخضار', 'اخلط المكونات'],
            'nutrition': {'calories': 450, 'protein': 35, 'carbs': 15, 'fat': 25},
            'preparationTime': 15,
            'cookingTime': 20,
            'difficulty': 'متوسط',
            'allergens': [],
          },
        ]);
        break;
      case 'العشاء':
        templates.addAll([
          {
            'name': 'سمك مشوي مع الخضار',
            'description': 'سمك مشوي صحي مع خضار مطبوخة',
            'ingredients': ['سمك', 'بروكلي', 'جزر', 'زيت زيتون', 'ليمون'],
            'instructions': ['اشوي السمك', 'اطبخ الخضار على البخار', 'قدم مع الليمون'],
            'nutrition': {'calories': 350, 'protein': 30, 'carbs': 20, 'fat': 15},
            'preparationTime': 10,
            'cookingTime': 25,
            'difficulty': 'متوسط',
            'allergens': [],
          },
        ]);
        break;
    }
    
    return templates.isNotEmpty ? templates : [templates.first];
  }

  List<Ingredient> _generateIngredients(List<String> ingredientNames) {
    return ingredientNames.map((name) => Ingredient(
      name: name,
      amount: 100.0,
      unit: 'جرام',
    )).toList();
  }

  NutritionInfo _adjustNutritionToCalories(Map<String, dynamic> baseNutrition, int targetCalories) {
    final baseCals = baseNutrition['calories'] as int;
    final ratio = targetCalories / baseCals;
    
    return NutritionInfo(
      calories: targetCalories,
      protein: (baseNutrition['protein'] * ratio),
      carbs: (baseNutrition['carbs'] * ratio),
      fat: (baseNutrition['fat'] * ratio),
      fiber: 5.0,
      sugar: 10.0,
      sodium: 500,
    );
  }

  List<String> _filterAllergens(List<String> allergens, List<String> userAllergies) {
    return allergens.where((allergen) => !userAllergies.contains(allergen)).toList();
  }

  NutritionInfo _calculateTotalNutrition(List<Meal> meals) {
    return NutritionInfo(
      calories: meals.fold(0, (sum, meal) => sum + meal.nutrition.calories),
      protein: meals.fold(0.0, (sum, meal) => sum + meal.nutrition.protein),
      carbs: meals.fold(0.0, (sum, meal) => sum + meal.nutrition.carbs),
      fat: meals.fold(0.0, (sum, meal) => sum + meal.nutrition.fat),
      fiber: meals.fold(0.0, (sum, meal) => sum + meal.nutrition.fiber),
      sugar: meals.fold(0.0, (sum, meal) => sum + meal.nutrition.sugar),
      sodium: meals.fold(0, (sum, meal) => sum + meal.nutrition.sodium),
    );
  }
}
