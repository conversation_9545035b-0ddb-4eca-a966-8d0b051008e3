import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_model.dart';
import 'firebase_service.dart';

/// خدمة المصادقة والتسجيل
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseService _firebaseService = FirebaseService();

  /// الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _auth.currentUser?.uid;

  /// التحقق من تسجيل الدخول
  bool get isSignedIn => _auth.currentUser != null;

  /// تدفق حالة المصادقة
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // ==================== تسجيل الدخول والتسجيل ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );
      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  /// إنشاء حساب جديد بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      // تحديث اسم المستخدم
      await credential.user?.updateDisplayName(name);

      // إرسال بريد التحقق
      await sendEmailVerification();

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول باستخدام Google
  Future<UserCredential> signInWithGoogle() async {
    try {
      // بدء عملية تسجيل الدخول
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        throw Exception('تم إلغاء تسجيل الدخول');
      }

      // الحصول على تفاصيل المصادقة
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // إنشاء بيانات الاعتماد
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // تسجيل الدخول إلى Firebase
      final userCredential = await _auth.signInWithCredential(credential);

      // التحقق من كون المستخدم جديد
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        // إنشاء ملف تعريف أساسي للمستخدم الجديد
        await _createBasicUserProfile(userCredential.user!);
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('فشل في تسجيل الدخول باستخدام Google: $e');
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      throw Exception('فشل في تسجيل الخروج: $e');
    }
  }

  // ==================== إدارة كلمة المرور ====================

  /// إرسال بريد إعادة تعيين كلمة المرور
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('فشل في إرسال بريد إعادة التعيين: $e');
    }
  }

  /// تغيير كلمة المرور
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إعادة المصادقة
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // تحديث كلمة المرور
      await user.updatePassword(newPassword);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('فشل في تغيير كلمة المرور: $e');
    }
  }

  // ==================== التحقق من البريد الإلكتروني ====================

  /// إرسال بريد التحقق
  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      throw Exception('فشل في إرسال بريد التحقق: $e');
    }
  }

  /// إعادة تحميل بيانات المستخدم
  Future<void> reloadUser() async {
    try {
      await _auth.currentUser?.reload();
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المستخدم: $e');
    }
  }

  /// التحقق من تأكيد البريد الإلكتروني
  bool get isEmailVerified => _auth.currentUser?.emailVerified ?? false;

  // ==================== إدارة الحساب ====================

  /// تحديث الملف الشخصي
  Future<void> updateProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        await user.updateDisplayName(displayName);
        await user.updatePhotoURL(photoURL);
      }
    } catch (e) {
      throw Exception('فشل في تحديث الملف الشخصي: $e');
    }
  }

  /// حذف الحساب
  Future<void> deleteAccount(String password) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إعادة المصادقة
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );
      await user.reauthenticateWithCredential(credential);

      // حذف بيانات المستخدم من Firestore
      // TODO: إضافة منطق حذف البيانات

      // حذف الحساب
      await user.delete();
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('فشل في حذف الحساب: $e');
    }
  }

  // ==================== الدوال المساعدة ====================

  /// إنشاء ملف تعريف أساسي للمستخدم الجديد
  Future<void> _createBasicUserProfile(User user) async {
    try {
      final userModel = UserModel(
        id: user.uid,
        name: user.displayName ?? 'مستخدم جديد',
        email: user.email ?? '',
        photoUrl: user.photoURL,
        gender: '',
        birthDate: DateTime.now().subtract(const Duration(days: 365 * 25)), // عمر افتراضي 25 سنة
        height: 170.0,
        currentWeight: 70.0,
        targetWeight: 70.0,
        goal: '',
        activityLevel: '',
        dietType: '',
        allergies: [],
        mealsPerDay: 3,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firebaseService.saveUserProfile(userModel);
    } catch (e) {
      // تسجيل الخطأ ولكن عدم إيقاف العملية
      // يمكن استخدام logger هنا بدلاً من print
      debugPrint('فشل في إنشاء ملف تعريف المستخدم: $e');
    }
  }

  /// معالجة استثناءات Firebase Auth
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'لا يوجد مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'requires-recent-login':
        return 'يتطلب تسجيل دخول حديث';
      default:
        return 'حدث خطأ في المصادقة: ${e.message}';
    }
  }
}
