import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج قياس الوزن
class WeightEntry {
  final String id;
  final String userId;
  final double weight;
  final DateTime date;
  final String? notes;
  final DateTime createdAt;

  WeightEntry({
    required this.id,
    required this.userId,
    required this.weight,
    required this.date,
    this.notes,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'weight': weight,
      'date': Timestamp.fromDate(date),
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory WeightEntry.fromMap(Map<String, dynamic> map) {
    return WeightEntry(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      weight: (map['weight'] ?? 0).toDouble(),
      date: (map['date'] as Timestamp).toDate(),
      notes: map['notes'],
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }
}

/// نموذج تسجيل التمرين المنجز
class WorkoutLog {
  final String id;
  final String userId;
  final String workoutId;
  final String workoutName;
  final int duration; // بالدقائق
  final int caloriesBurned;
  final DateTime date;
  final bool completed;
  final String? notes;
  final DateTime createdAt;

  WorkoutLog({
    required this.id,
    required this.userId,
    required this.workoutId,
    required this.workoutName,
    required this.duration,
    required this.caloriesBurned,
    required this.date,
    required this.completed,
    this.notes,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'workoutId': workoutId,
      'workoutName': workoutName,
      'duration': duration,
      'caloriesBurned': caloriesBurned,
      'date': Timestamp.fromDate(date),
      'completed': completed,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory WorkoutLog.fromMap(Map<String, dynamic> map) {
    return WorkoutLog(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      workoutId: map['workoutId'] ?? '',
      workoutName: map['workoutName'] ?? '',
      duration: map['duration'] ?? 0,
      caloriesBurned: map['caloriesBurned'] ?? 0,
      date: (map['date'] as Timestamp).toDate(),
      completed: map['completed'] ?? false,
      notes: map['notes'],
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }
}

/// نموذج إحصائيات التقدم
class ProgressStats {
  final double currentWeight;
  final double targetWeight;
  final double startWeight;
  final double weightLost;
  final double weightToGo;
  final int totalWorkouts;
  final int totalCaloriesBurned;
  final int averageWorkoutDuration;
  final DateTime lastWeightEntry;
  final DateTime lastWorkout;

  ProgressStats({
    required this.currentWeight,
    required this.targetWeight,
    required this.startWeight,
    required this.weightLost,
    required this.weightToGo,
    required this.totalWorkouts,
    required this.totalCaloriesBurned,
    required this.averageWorkoutDuration,
    required this.lastWeightEntry,
    required this.lastWorkout,
  });

  /// نسبة التقدم نحو الهدف
  double get progressPercentage {
    if (startWeight == targetWeight) return 100.0;
    final totalToLose = (startWeight - targetWeight).abs();
    final currentProgress = (startWeight - currentWeight).abs();
    return (currentProgress / totalToLose * 100).clamp(0.0, 100.0);
  }

  /// متوسط فقدان الوزن الأسبوعي
  double get weeklyWeightLoss {
    final daysSinceStart = DateTime.now().difference(lastWeightEntry).inDays;
    if (daysSinceStart <= 0) return 0.0;
    final weeks = daysSinceStart / 7;
    return weightLost / weeks;
  }

  /// BMI الحالي
  double calculateBMI(double height) {
    final heightInMeters = height / 100;
    return currentWeight / (heightInMeters * heightInMeters);
  }
}

/// نموذج نقطة بيانات للرسم البياني
class ChartDataPoint {
  final DateTime date;
  final double value;
  final String label;

  ChartDataPoint({
    required this.date,
    required this.value,
    required this.label,
  });
}

/// نموذج بيانات الرسم البياني
class ChartData {
  final List<ChartDataPoint> weightData;
  final List<ChartDataPoint> caloriesData;
  final List<ChartDataPoint> workoutData;
  final DateTime startDate;
  final DateTime endDate;

  ChartData({
    required this.weightData,
    required this.caloriesData,
    required this.workoutData,
    required this.startDate,
    required this.endDate,
  });

  /// الحصول على أقل وأعلى قيمة للوزن
  double get minWeight => weightData.isEmpty ? 0 : weightData.map((e) => e.value).reduce((a, b) => a < b ? a : b);
  double get maxWeight => weightData.isEmpty ? 0 : weightData.map((e) => e.value).reduce((a, b) => a > b ? a : b);

  /// الحصول على أقل وأعلى قيمة للسعرات
  double get minCalories => caloriesData.isEmpty ? 0 : caloriesData.map((e) => e.value).reduce((a, b) => a < b ? a : b);
  double get maxCalories => caloriesData.isEmpty ? 0 : caloriesData.map((e) => e.value).reduce((a, b) => a > b ? a : b);

  /// الحصول على أقل وأعلى قيمة للتمارين
  double get minWorkout => workoutData.isEmpty ? 0 : workoutData.map((e) => e.value).reduce((a, b) => a < b ? a : b);
  double get maxWorkout => workoutData.isEmpty ? 0 : workoutData.map((e) => e.value).reduce((a, b) => a > b ? a : b);
}
